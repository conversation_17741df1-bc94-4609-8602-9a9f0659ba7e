import OpenAI from 'openai';
import { Message, ToolCall, ToolCallResult } from './api';
import z from 'zod';
import { SearchFilters } from './memory';

export type ChatStatus = 'uninitialized' | 'idle' | 'running' | 'streaming' | 'calling_tool';

export interface MemoryOptions {
  recent: number; // number of recent messages to include
  related: number; //top k related messages to include
  site: number; // number of site messages to include
  graph: number; // number of graph messages to include
  tab: boolean; // if to include current tab info
}

export interface ChatOptions {
  conversationId: number;
  onMessageUpdate?: (message: Message) => Promise<void>;
  onMessageComplete?: (message: Message) => Promise<void>;
  onStatusChange?: (status: ChatStatus) => Promise<void>;
}

export interface ToolOptions {
  filters?: SearchFilters;
  metadata?: Record<string, unknown>;
  onToolMessageUpdate?: (content: any) => Promise<void>;
}

export interface AgentRuntimeConfig {
  chatOptions?: ChatOptions;
  toolOptions?: ToolOptions;
  task_id?: string;
}

export interface ToolExecutor {
  execute: (toolCall: ToolCall, options?: AgentRuntimeConfig) => Promise<ToolCallResult>;

  getTools: () => OpenAI.ChatCompletionTool[];

  getPostToolcallMessage: (toolCall: ToolCall) => string;

  isFinalToolCall?: (toolCall: ToolCall) => boolean;
}

export interface ContextBuilder {
  build: (message: Message, options?: Partial<MemoryOptions>) => Promise<Message[]>;
}

export interface AgentConfig {
  id: string;
  llmClient: OpenAI;
  model: string;
  systemPrompt: string;
  contextBuilder: ContextBuilder;
  toolExecutor: ToolExecutor;
  maxToolCalls: number;
  maxDepth?: number; // max depth of the task tree
}

export interface Agent {
  // infer the next message until the end of the conversation
  run: (userMessage: Message, runtimeConfig?: AgentRuntimeConfig) => Promise<Message[]>;

  getConfig: () => AgentConfig;

  abort(): Promise<void>;
}

export const SimpleTaskSchema = z.object({
  id: z.string(),
  goal: z.string(),
  output: z.string(),
  inputsFrom: z.array(z.string()).optional(),
  canBeAtomic: z.boolean().optional(),
});

export const ForeachTaskSchema = SimpleTaskSchema.extend({
  foreach: z.object({
    count: z.number().optional(),
    task: SimpleTaskSchema,
  }),
});

export const LoopTaskSchema = SimpleTaskSchema.extend({
  loop: z.object({
    until: SimpleTaskSchema,
    task: SimpleTaskSchema,
  }),
});

export const ConditionTaskSchema = SimpleTaskSchema.extend({
  condition: z.object({
    taskList: z.array(
      z.object({
        when: z.string(),
        task: SimpleTaskSchema,
      })
    ),
  }),
});

export type SimpleTask = z.infer<typeof SimpleTaskSchema>;
export type ForeachTask = z.infer<typeof ForeachTaskSchema>;
export type LoopTask = z.infer<typeof LoopTaskSchema>;
export type ConditionTask = z.infer<typeof ConditionTaskSchema>;
export type Task = SimpleTask | ForeachTask | LoopTask | ConditionTask;
