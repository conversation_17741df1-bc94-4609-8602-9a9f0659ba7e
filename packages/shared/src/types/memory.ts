import { z } from 'zod';
import { MemoryItem } from './api';
import { MessageSchema } from './api';

export interface MemoryRelation {
  source: string;
  relationship: string;
  destination: string;
}

export interface SearchFilters {
  userId?: string;
  conversationId?: string;
  hostname?: string;
  faqId?: string;
}

export interface AddMemoryOptions {
  metadata?: Record<string, any>;
  filters?: SearchFilters;
}

export interface SearchResult {
  results: MemoryItem[];
  relations?: any[];
}

export const SearchFiltersSchema = z.object({
  userId: z.string().optional(),
  conversationId: z.string().optional(),
  hostname: z.string().optional(),
  faqId: z.string().optional(),
});

export const SearchMemoryOptionsSchema = z.object({
  limit: z.number(),
  filters: SearchFiltersSchema,
});

export const AddMemoryOptionsSchema = z.object({
  metadata: z.record(z.any()).optional(),
  filters: SearchFiltersSchema,
});

export type SearchMemoryOptions = z.infer<typeof SearchMemoryOptionsSchema>;

export const SearchMemoryRequestSchemaV2 = z.object({
  text: z.string(),
  config: SearchMemoryOptionsSchema,
});

/**
 * api-cf request memory-server
 */
export type SearchMemoryRequestV2 = z.infer<typeof SearchMemoryRequestSchemaV2>;

export const AddSiteMessageRequestSchema = z.object({
  messages: z.array(MessageSchema),
  config: AddMemoryOptionsSchema,
});

export type AddSiteMessageRequest = z.infer<typeof AddSiteMessageRequestSchema>;
