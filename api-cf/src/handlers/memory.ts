import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { GatewayServiceError } from '../types/service';
import { getUserBalance } from '../d1/user';
import { deductUserCredits } from '../d1/user';
import { EMBEDDING_MODEL } from '../utils/common';
import { addMessagesToMemory, collectText, searchMemory } from '../utils/memory';
import {
  Message,
  AddMemoryRequestSchema,
  SearchMemoryRequestSchema,
  SearchMemoryResponseSchema,
  AddMemoryResponseSchema,
  AddMemoryOptions,
} from '@the-agent/shared';

export class SearchMemory extends OpenAPIRoute {
  schema = {
    request: {
      query: SearchMemoryRequestSchema,
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: SearchMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId') || 'rJh83ZRqcBeQA1BzkDjvu1mgJOy1';
    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    const { text, limit, conversationId } = c.req.query();
    if (!text || !conversationId) {
      throw new GatewayServiceError(400, 'Invalid request');
    }
    console.error('[DEBUG] SearchMemory text:', text);
    console.error('[DEBUG] SearchMemory conversationId:', conversationId);
    const result = await searchMemory(c.env, text, {
      limit: parseInt(limit ?? '3'),
      filters: {
        userId,
        conversationId,
      },
    });
    console.error('[DEBUG] SearchMemory result=============:', result);

    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 + text.length / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(
      {
        results: result.results,
        relations: result.relations,
      },
      200
    );
  }
}

export class AddMemory extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: AddMemoryRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: AddMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    let request: { messages: Message[] };
    try {
      request = AddMemoryRequestSchema.parse(body);
    } catch (error) {
      console.error('Invalid message format:', error);
      throw new GatewayServiceError(400, 'Invalid message format');
    }

    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    if (!request.messages) {
      throw new GatewayServiceError(400, 'Invalid request');
    }

    const processed: Message[] = request.messages.map(message => ({
      id: message.id,
      conversation_id: message.conversation_id,
      role: message.role,
      content: collectText(message),
      status: message.status,
    }));

    const config: AddMemoryOptions = {
      metadata: {},
      filters: {
        userId,
      },
    };
    const messages: Message[] = processed.filter(p => (p.content?.length ?? 0) > 0);

    const result = await addMessagesToMemory(c.env, messages, config);

    const totalTextLength = processed.reduce((acc, p) => acc + (p.content?.length ?? 0), 0);
    const totalMessages = messages.length;
    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 * totalMessages + totalTextLength / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(result, 200);
  }
}
