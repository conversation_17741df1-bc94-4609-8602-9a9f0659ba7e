import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import {
  SaveSiteMessageRequestSchema,
  SaveSiteMessageResponseSchema,
  SearchMemoryOptions,
  SearchSiteMessageRequestSchema,
  SearchSiteMessageResponseSchema,
} from '@the-agent/shared';
import { GatewayServiceError } from '../types/service';

import { addSiteMessageToMemory, extractSiteIdFromUrl, searchMemory } from '../utils/memory';
// Save site message implementation
export class SaveSiteMessage extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SaveSiteMessageRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Site message saved successfully to global memory',
        content: {
          'application/json': {
            schema: SaveSiteMessageResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const userEmail = c.get('userEmail');
    const body = await c.req.json();

    // Check if user email is in whitelist
    const whitelist = c.env.SAVE_SITE_EMAIL_WHITELIST;
    if (whitelist) {
      const whitelistedEmails = whitelist
        .split(',')
        .map((email: string) => email.trim().toLowerCase());
      const normalizedUserEmail = userEmail?.toLowerCase();

      if (!normalizedUserEmail || !whitelistedEmails.includes(normalizedUserEmail)) {
        throw new GatewayServiceError(403, 'User not authorized to save site messages');
      }
    } else {
      // If no whitelist is configured, deny access for security
      throw new GatewayServiceError(403, 'Site message saving is not configured');
    }

    // Validate the faqs
    if (!body.faqs || !Array.isArray(body.faqs) || body.faqs.length === 0) {
      throw new GatewayServiceError(400, 'FAQs array is required and cannot be empty');
    }

    // Validate site
    if (!body.site || typeof body.site !== 'string') {
      throw new GatewayServiceError(400, 'Site URL is required');
    }

    const hostname = extractSiteIdFromUrl(body.site);

    const results = [];

    try {
      // Process each FAQ
      for (const faq of body.faqs) {
        if (!faq.question || !faq.answer) {
          console.warn('Skipping invalid FAQ:', faq);
          continue;
        }

        const faqId = crypto.randomUUID();
        const now = new Date().toISOString();

        // 1. Save to supabase memories (via memory server) for vector search
        try {
          const memoryConfig = {
            metadata: {},
            filters: {
              hostname,
              faqId,
            },
          };

          await addSiteMessageToMemory(
            c.env,
            [
              {
                conversation_id: 0,
                id: Number(faqId),
                role: 'user',
                content: faq.question,
                status: 'completed',
              },
            ],
            memoryConfig
          );
        } catch (error) {
          console.error('Failed to save FAQ to memory:', error);
          // Continue processing other FAQs even if one fails
        }

        // 2. Save to cloudflare D1 knowledge_base for relational storage
        try {
          const result = await c.env.DB.prepare(
            'INSERT INTO knowledge_base (faq_id, user_id, hostname, question, answer, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)'
          )
            .bind(faqId, userId, hostname, faq.question, faq.answer, now, now)
            .run();

          if (result.success) {
            results.push({
              id: result.meta.last_row_id,
              faq_id: faqId,
              question: faq.question,
              answer: faq.answer,
              hostname,
            });
          } else {
            console.error('Failed to save FAQ to knowledge_base:', result.error);
          }
        } catch (error) {
          console.error('Failed to save FAQ to knowledge_base:', error);
        }
      }

      return c.json(
        {
          results,
        },
        200
      );
    } catch (error) {
      console.error('Error in SaveSiteMessage:', error);
      throw new GatewayServiceError(500, 'Failed to save site messages');
    }
  }
}

// Search site message implementation
export class SearchSiteMessage extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SearchSiteMessageRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Site message searched successfully from global memory',
        content: {
          'application/json': {
            schema: SearchSiteMessageResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const body = await c.req.json();

    // Validate site
    if (!body.site || typeof body.site !== 'string') {
      throw new GatewayServiceError(400, 'Site URL is required');
    }

    // Validate user_message
    if (!body.user_message || typeof body.user_message !== 'string') {
      throw new GatewayServiceError(400, 'User message is required');
    }

    if (body.limit && typeof body.limit !== 'number') {
      throw new GatewayServiceError(400, 'Limit must be a number');
    }
    const limit = body.limit ?? 5;

    try {
      // 1. Search similar questions from memories using vector search
      const hostname = extractSiteIdFromUrl(body.site);
      const searchConfig: SearchMemoryOptions = {
        limit,
        filters: {
          hostname,
        },
      };

      const searchResult = await searchMemory(c.env, body.user_message, searchConfig);

      // If no similar questions found, return empty array
      if (!searchResult.results || searchResult.results.length === 0) {
        return c.json(
          {
            related_site_messages: [],
          },
          200
        );
      }

      // 2. Get question and answer pairs from knowledge_base
      // Filter out null/undefined IDs using type guard
      const faqIds = searchResult.results
        .map(item => item.metadata?.id)
        .filter((id): id is string => id !== undefined && id !== null);

      if (faqIds.length === 0) {
        return c.json(
          {
            related_site_messages: [],
          },
          200
        );
      }

      const placeholders = faqIds.map(() => '?').join(',');

      const stmt = c.env.DB.prepare(
        `SELECT faq_id, question, answer FROM knowledge_base 
         WHERE faq_id IN (${placeholders})`
      );

      const result = await stmt.bind(...faqIds).all();

      const relatedMessages = result.results.map(
        (row: { faq_id: string; question: string; answer: string }) => ({
          faq_id: row.faq_id,
          question: row.question,
          answer: row.answer,
        })
      );

      return c.json(
        {
          related_site_messages: relatedMessages,
        },
        200
      );
    } catch (error) {
      console.error('Error in SearchSiteMessage:', error);
      throw new GatewayServiceError(500, 'Failed to search site messages');
    }
  }
}
