import { Message } from '@the-agent/shared';

export function buildSearchFilters(raw: Record<string, unknown> | undefined): {
  userId: string;
  conversationId?: string;
  [key: string]: unknown;
} {
  if (!raw || typeof raw.userId !== 'string' || !raw.userId.trim()) {
    throw new Error('userId is required in filters for SearchMemory tool');
  }
  const { userId, conversationId, ...rest } = raw;
  const filters: { userId: string; conversationId?: string; [key: string]: unknown } = { userId };
  if (conversationId && typeof conversationId === 'string') {
    filters.conversationId = conversationId;
  }
  Object.assign(filters, rest);
  return filters;
}

export function generateUserMessageWithContext(messages: Message[]): string {
  const context = messages
    .map(msg => {
      if (msg.role === 'user' || msg.role === 'assistant') {
        return `${msg.role}: ${msg.content}`;
      }
      if (msg.role === 'tool') {
        return `tool[${(msg as any).name || ''}]: ${(msg as any).result || msg.content || ''}`;
      }
      if (msg.tool_calls) {
        return `tool_calls: ${JSON.stringify(msg.tool_calls)}`;
      }
      return JSON.stringify(msg);
    })
    .join('\n');

  return `
# Conversation Context
${context}

# Memory Management Instructions
1. Extract all important, atomic facts from the above conversation.
2. For each new fact:
   - If you believe it may duplicate or be highly similar to existing memories, call the SearchMemory tool for that fact.
   - If SearchMemory returns any memory with exactly the same content, call MemoryOps to delete ALL such memories, then add the new fact.
   - If SearchMemory returns any memory with high similarity (score > 0.75), aggregate all similar facts (including the new one) into a single concise fact using the LLM, call MemoryOps to delete ALL similar old memories, then add the aggregation.
   - If no similar or identical memory, add directly with MemoryOps.
3. Always provide explicit reasoning before making any tool call.
4. Only call SearchMemory if you believe deduplication is needed.
5. All aggregation, deduplication, and deletion must be based on SearchMemory results and your reasoning.

## Tool Argument Format
- Tool arguments MUST be a strict object format:
  { "operations": [ { "action": "add", "facts": ["fact1", "fact2"] } ] }
- NEVER wrap the object in an array (e.g., [ { operations: [...] } ] is incorrect).
- Incorrect example:
  [ { "operations": [ ... ] } ] // Incorrect!
- Correct example:
  { "operations": [ ... ] } // Correct!
`;
}
