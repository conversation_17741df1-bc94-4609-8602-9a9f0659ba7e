import { z } from 'zod';
import { MemoryItem, SearchFilters } from '@the-agent/shared';

// ============================================================================
// Memory-server local type definitions - separated from Message types
// ============================================================================
export interface VectorInsertPayload {
  id?: string;
  text: string;
  metadata?: Record<string, unknown>;
  created_at?: string;
}

// LLMRuntime related types
export interface EmbeddingResponse {
  data: Array<{
    embedding: number[];
    index: number;
  }>;
}

export interface AddMemoryResult {
  success: boolean;
  factIds: string[];
}

export interface DeleteMemoryParams {
  ids: string[];
}

export interface DeleteMemoryResult {
  success: boolean;
  deleted: number;
}

// Tool context types
export interface VectorStoreInterface {
  search: (
    vector: number[],
    limit?: number,
    filters?: SearchFilters,
    scoreThreshold?: number
  ) => Promise<MemoryItem[]>;
  insert: (vectors: number[][], payloads: VectorInsertPayload[]) => Promise<string[]>;
  update: (id: string, newContent: string) => Promise<void>;
  delete: (ids: string[]) => Promise<void>;
}

export interface LLMRuntimeInterface {
  embed: (text: string) => Promise<number[]>;
  embedBatch?: (texts: string[]) => Promise<number[][]>;
}

export interface MemoryOptions {
  metadata?: Record<string, unknown>;
  filters?: SearchFilters;
}

export interface MemoryContext {
  vectorStore: VectorStoreInterface;
  llmRuntime: LLMRuntimeInterface;
}

// Tool definition types
export interface MemoryTool {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, unknown>;
    required?: string[];
  };
  execute: (params: unknown, context: MemoryContext) => Promise<unknown>;
}

// Configuration types
export interface MemoryConfig {
  openrouter: {
    model: string;
    apiKey: string;
  };
  embedding: {
    apiKey: string;
    model: string;
  };
  supabase: {
    supabaseUrl: string;
    supabaseKey: string;
    tableName: string;
    embeddingColumnName: string;
    metadataColumnName: string;
  };
}

export const DeleteMemoryParamsSchema = z.object({
  ids: z.array(z.string()),
});
