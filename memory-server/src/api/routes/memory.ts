import { Router } from 'express';
import { MemoryService } from '../services/memory';

export const createMemoryRoutes = (service: MemoryService): Router => {
  const router = Router();

  console.error('[DEBUG] createMemoryRoutes:🚀🚀');

  router.post('/', service.addMemory.bind(service));
  router.post('/add', service.addMemory.bind(service));
  router.post('/search', service.searchMemories.bind(service));
  router.post('/add_site', service.addSiteMessage.bind(service));

  return router;
};
