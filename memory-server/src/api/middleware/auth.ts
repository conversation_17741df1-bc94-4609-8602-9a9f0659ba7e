import { Request, Response, NextFunction } from 'express';
import { config } from '../../config';
import { logger } from '../../utils/logger';

export function authMiddleware(req: Request, res: Response, next: NextFunction) {
  const auth = req.headers.authorization;
  const apiKey = config.apiKey;
  console.error('[DEBUG] authMiddleware auth:', auth);
  logger.info('[DEBUG] authMiddleware apiKey:', apiKey);
  if (!auth || !auth.startsWith('Bearer ') || !apiKey) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  const token = auth.slice(7);
  if (token !== apiKey) {
    logger.error('[DEBUG] authMiddleware token:', token);
    logger.error('[DEBUG] authMiddleware apiKey:', apiKey);
    return res.status(403).json({ error: 'Forbidden' });
  }
  next();
}
