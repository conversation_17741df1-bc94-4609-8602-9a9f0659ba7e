import { Request, Response } from 'express';
import type { VectorStoreInterface, LLMRuntimeInterface } from '../../types/memory';
import {
  AddMemoryResponse,
  AddSiteMessageRequest,
  ChatAgent,
  MessageRole,
  SearchMemoryRequestV2,
} from '@the-agent/shared';
import { searchMemories } from '../../tools/search';
import { generateUserMessageWithContext } from '../../utils/memory';

export class MemoryService {
  constructor(
    private memoryAgent: ChatAgent,
    private vectorStore: VectorStoreInterface,
    private llmRuntime: LLMRuntimeInterface
  ) {}

  /**
   * Add memory with preprocessing: concatenate all messages (LLM standard format) into a single context string,
   * then pass as a single message to memory<PERSON>gent. This ensures memory<PERSON>gent receives a summarized, context-rich input
   * rather than the raw multi-turn message array. This is useful for agents that expect a single user message as input.
   *
   * Processing logic:
   * - Accepts 'messages' (array, LLM standard chat format: user/assistant/tool/function_call...)
   * - Concatenates all messages into a readable context string:
   *   - user/assistant: 'role: content'
   *   - tool: 'tool[name]: result/content'
   *   - function_call: 'function_call: ...'
   *   - other: JSON.stringify(msg)
   * - Wraps the context as a single message and passes to memoryAgent
   *
   * @param req {
   *   messages: [
   *     { role: 'user', content: '...' },
   *     { role: 'assistant', content: '...' },
   *     { role: 'tool', name: '...', result: '...' },
   *     ...
   *   ],
   *   config: { filters: { userId: string}, metadata: {} }
   * }
   * @param res { ...memoryAgent result... }
   */
  async addMemory(req: Request, res: Response) {
    try {
      const { messages, config } = req.body;

      if (!messages || !Array.isArray(messages) || messages.length === 0) {
        return res
          .status(400)
          .json({ error: 'messages is required and must be a non-empty array' });
      }
      if (!config || !config.filters?.userId) {
        return res.status(400).json({ error: 'Missing required field: config.userId is required' });
      }

      // verify all messages have the same conversation_id
      const conversationIds = Array.from(new Set(messages.map((m: any) => m.conversation_id)));
      if (conversationIds.length > 1) {
        return res.status(400).json({ error: 'All messages must have the same conversation_id' });
      }
      const conversationId = conversationIds[0];

      // filters must have conversationId, if not, fill it
      if (!config.filters) config.filters = {};
      if (!config.filters.conversationId) {
        if (conversationId === undefined || conversationId === null) {
          return res
            .status(400)
            .json({ error: 'conversationId is required in filters or in messages' });
        }
        config.filters.conversationId = conversationId;
      }
      res.status(201).json({
        results: [],
        relations: [],
      });

      const enhancedContext = await generateUserMessageWithContext(messages);

      const userMessage = {
        id: Date.now(),
        conversation_id: conversationId,
        role: 'user' as MessageRole,
        content: enhancedContext,
      };
      await this.memoryAgent.run(userMessage, {
        toolOptions: { filters: config.filters, metadata: config.metadata },
      });
    } catch (error: any) {
      console.error('addMemory error', error);
    }
  }

  /**
   * Enhanced search with better error handling and result formatting
   *
   * @param req {
      query: 'Hello, this is a test message for memory search success',
      config: {
        limit: 100,
        filters: {  // at least one of userId, conversationId, hostname, faqId is required
          userId: '48a02574f02e8dad07b7c3478ab81a042a59054a46a50fa15bed63e8270cec52',
          conversationId: 1747563300,
          hostname: 'www.baidu.com',
          faqId: 1,
        },
      }
    }
   * @param res Service - Search Success: { results: [], relations: [ {
      source: 'local-xiangnuan',
      relationship: 'search_memory',
      destination: 'a_memory'
    }] }
   */
  async searchMemories(req: Request, res: Response) {
    try {
      const { text, config } = req.body as SearchMemoryRequestV2;

      if (!text) {
        return res.status(400).json({ error: 'text is required' });
      }
      if (!config || !config.filters) {
        return res.status(400).json({ error: 'config.filters is required' });
      }

      const context = {
        vectorStore: this.vectorStore,
        llmRuntime: this.llmRuntime,
      };

      const result = await searchMemories(
        {
          text,
          limit: config.limit,
          filters: config.filters,
        },
        context
      );

      res.json(result);
    } catch (error: any) {
      console.error('searchMemories error', error);
      res.status(400).json({ results: [], relations: [] });
    }
  }

  async addSiteMessage(req: Request, res: Response) {
    try {
      const { messages, config } = req.body as AddSiteMessageRequest;

      if (!config || !config.filters || !config.filters.hostname) {
        return res.status(400).json({ error: 'config.filters.hostname is required' });
      }
      if (!config || !config.filters || !config.filters.faqId) {
        return res.status(400).json({ error: 'config.filters.faqId is required' });
      }

      res.status(201).json({ success: true });

      const enhancedContext = await generateUserMessageWithContext(messages);

      const userMessage = {
        id: Date.now(),
        conversation_id: 0,
        role: 'user' as MessageRole,
        content: enhancedContext,
      };
      await this.memoryAgent.run(userMessage, {
        toolOptions: { filters: config.filters, metadata: config.metadata },
      });
    } catch (error: any) {
      console.error('addSiteMessage error', error);
    }
  }
}
