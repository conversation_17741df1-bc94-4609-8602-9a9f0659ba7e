import { LLMRuntimeInterface } from '../types/memory';

export class LL<PERSON><PERSON><PERSON> implements LLMRuntimeInterface {
  private embeddingApiKey: string;
  private embeddingEndpoint: string;
  private embeddingModel: string;

  constructor(apiKey: string, model: string = 'intfloat/multilingual-e5-large') {
    this.embeddingApiKey = apiKey;
    this.embeddingModel = model;
    this.embeddingEndpoint = 'https://api.deepinfra.com/v1/openai/embeddings';
  }

  // single text embedding
  async embed(text: string): Promise<number[]> {
    try {
      const response = await fetch(this.embeddingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.embeddingApiKey}`,
        },
        body: JSON.stringify({
          model: this.embeddingModel,
          input: text,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`DeepInfra API error: ${response.status} ${errorText}`);
      }

      const json = (await response.json()) as { data: { embedding: number[] }[] };

      return json.data[0].embedding;
    } catch (error) {
      console.error('[LLMRuntime] DeepInfra embedding error:', error);
      throw error;
    }
  }

  // batch text embedding
  async embedBatch(texts: string[]): Promise<number[][]> {
    try {
      const response = await fetch(this.embeddingEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.embeddingApiKey}`,
        },
        body: JSON.stringify({
          model: this.embeddingModel,
          input: texts,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`DeepInfra API error: ${response.status} ${errorText}`);
      }

      const data = (await response.json()) as { data: { embedding: number[] }[] };

      return data.data.map(item => item.embedding);
    } catch (error) {
      console.error('[LLMRuntime] DeepInfra batch embedding error:', error);
      throw error;
    }
  }
}
