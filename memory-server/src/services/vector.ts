import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { VectorInsertPayload, VectorStoreInterface } from '../types/memory';
import { MemoryItem } from '@the-agent/shared';
import { config } from '../config';
import crypto from 'crypto';

/*
SQL Migration to run in Supabase SQL Editor:

--  enable vector extension (can be repeated if not enabled)
create extension if not exists vector;

-- create/upgrade vector similarity search function with min_score
create or replace function match_vectors(
  query_embedding vector(1024),
  match_count int,
  filter jsonb default '{}'::jsonb,
  min_score float default 0.0
)
returns table (
  id text,
  similarity float,
  metadata jsonb
)
language plpgsql
as $$
begin
  return query
  select
    t.id::text,
    1 - (t.embedding <=> query_embedding) as similarity,
    t.metadata
  from memories t
  where (case when filter::text = '{}'::text then true else t.metadata @> filter end)
    and (1 - (t.embedding <=> query_embedding)) >= min_score
  order by t.embedding <=> query_embedding
  limit match_count;
end;
$$;
*/
export class VectorStore implements VectorStoreInterface {
  private client: SupabaseClient;
  private table: string;
  private embeddingCol: string;
  private metadataCol: string;

  constructor() {
    const supabaseConfig = config.supabase;
    this.client = createClient(supabaseConfig.supabaseUrl, supabaseConfig.supabaseKey);
    this.table = supabaseConfig.tableName;
    this.embeddingCol = supabaseConfig.embeddingColumnName || 'embedding';
    this.metadataCol = supabaseConfig.metadataColumnName || 'metadata';
  }

  // insert vectors and payloads
  async insert(vectors: number[][], payloads: VectorInsertPayload[]): Promise<string[]> {
    const rows = vectors.map((embedding, i) => ({
      id: payloads[i].id || crypto.randomUUID(),
      [this.embeddingCol]: embedding,
      [this.metadataCol]: {
        ...payloads[i].metadata,
        data: payloads[i].text,
        created_at: payloads[i].created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    }));

    const { data, error } = await this.client.from(this.table).insert(rows).select('id');
    if (error) throw error;
    return (data || []).map((row: any) => row.id);
  }

  // similarity search with enhanced filtering and result formatting
  async search(
    vector: number[],
    limit: number = 10,
    filters?: Record<string, any>,
    scoreThreshold?: number
  ): Promise<MemoryItem[]> {
    try {
      const { data, error } = await this.client.rpc('match_vectors', {
        query_embedding: vector,
        match_count: limit,
        filter: filters || {},
        min_score: typeof scoreThreshold === 'number' ? scoreThreshold : 0.0,
      });

      if (error) {
        console.error('Vector search query error:', error);
        throw error;
      }

      return (data || []).map((item: any) => ({
        id: item.id,
        memory: item.metadata?.memory || item.metadata?.data || '',
        score: item.similarity,
        metadata: item.metadata || {},
        created_at: item.metadata?.created_at,
        updated_at: item.metadata?.updated_at,
      }));
    } catch (error) {
      console.error('Vector search error:', error);
      throw new Error(
        `Vector search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  // delete memories by IDs
  async delete(ids: string[]): Promise<void> {
    try {
      if (!ids.length) return;
      const { error } = await this.client.from(this.table).delete().in('id', ids);
      if (error) throw error;
    } catch (error) {
      console.error('Vector delete error:', error);
      throw new Error(`Delete failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // update memory by ID
  async update(id: string, newContent: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.table)
        .update({
          [`${this.metadataCol}`]: this.client.rpc('jsonb_set', {
            target: `${this.metadataCol}`,
            path: '{memory}',
            value: JSON.stringify(newContent),
            create_missing: true,
          }),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);
      if (error) throw error;
    } catch (error) {
      console.error('Vector update error:', error);
      throw new Error(`Update failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
