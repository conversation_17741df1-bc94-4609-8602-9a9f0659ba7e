const http = require('http');

const userId = '48a02574f02e8dad07b7c3478ab81a042a59054a46a50fa15bed63e8270cec52';
const conversationId = 1747563300;

async function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, res => {
      let data = '';
      res.on('data', chunk => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });
    req.on('error', error => {
      reject(error);
    });
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timed out'));
    });
    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function directSearch(query) {
  const directData = JSON.stringify({
    query,
    config: {
      filters: { userId, conversationId },
      limit: 5,
    },
  });
  const directResult = await makeRequest(
    {
      hostname: 'localhost',
      port: 3000,
      path: '/memories/search',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(directData),
      },
      timeout: 60000,
    },
    directData
  );
  return JSON.parse(directResult.data).results || [];
}

async function llmFactSearch(query) {
  const addData = JSON.stringify({
    message: [
      {
        role: 'user',
        content: query,
      },
    ],
    config: {
      userId,
      metadata: { conversationId },
      infer: true,
    },
  });
  const addResult = await makeRequest(
    {
      hostname: 'localhost',
      port: 3000,
      path: '/memories/add',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(addData),
      },
      timeout: 60000,
    },
    addData
  );
  const messages = JSON.parse(addResult.data);
  let factQuery = null;
  for (const msg of messages) {
    if (msg.role === 'tool' && msg.name === 'SearchMemory' && Array.isArray(msg.tool_calls)) {
      try {
        const args = JSON.parse(msg.tool_calls[0].function.arguments);
        if (args.query) {
          factQuery = args.query;
          break;
        }
      } catch (e) {}
    }
  }
  let searchResults = [];
  if (factQuery) {
    searchResults = await directSearch(factQuery);
  }
  return { factQuery, searchResults };
}

async function testCompareSearch(query) {
  const directMemories = await directSearch(query);
  let llmFact = { factQuery: null, searchResults: [] };
  try {
    llmFact = await llmFactSearch(query);
  } catch (e) {
    // ignore
  }
  const directIds = new Set(directMemories.map(m => m.id));
  const llmIds = new Set(llmFact.searchResults.map(m => m.id));
  const onlyDirect = [...directIds].filter(id => !llmIds.has(id));
  const onlyLLM = [...llmIds].filter(id => !directIds.has(id));
  return {
    query,
    directCount: directMemories.length,
    llmCount: llmFact.searchResults.length,
    onlyDirect,
    onlyLLM,
    factQuery: llmFact.factQuery,
    directMemories,
    llmMemories: llmFact.searchResults,
  };
}

async function batchTest(queries) {
  const results = [];
  for (const query of queries) {
    console.log('\n==============================');
    console.log('Test query:', query);
    const res = await testCompareSearch(query);
    results.push(res);
    console.log('[Direct embedding hits]', res.directCount);
    console.log('[LLM agent normalized+search hits]', res.llmCount);
    console.log('[LLM agent normalized fact]', res.factQuery);
    console.log('Only direct embedding hits:', res.onlyDirect.length, res.onlyDirect);
    if (res.onlyDirect.length > 0) {
      console.log('Only direct embedding hit details:');
      res.directMemories
        .filter(m => res.onlyDirect.includes(m.id))
        .forEach(m => {
          console.log(`  - [${m.id}]`, m.text, '|', m.metadata);
        });
    }
    console.log('Only LLM normalized hits:', res.onlyLLM.length, res.onlyLLM);
    if (res.onlyLLM.length > 0) {
      console.log('Only LLM normalized hit details:');
      res.llmMemories
        .filter(m => res.onlyLLM.includes(m.id))
        .forEach(m => {
          console.log(`  - [${m.id}]`, m.text, '|', m.metadata);
        });
    }
  }
  // Summary
  console.log('\n=== Batch Test Summary ===');
  let total = results.length;
  let allMatch = results.filter(
    r => r.directCount === r.llmCount && r.onlyDirect.length === 0 && r.onlyLLM.length === 0
  ).length;
  console.log('Total queries:', total);
  console.log('All matched:', allMatch);
  console.log('With differences:', total - allMatch);
}

// Batch query examples
const queries = [
  'I have been learning React and JavaScript programming recently',
  'TypeScript related experience',
  'I like to write code in TypeScript',
  'I am using React for frontend development',
  'I want to know the difference between TypeScript and React',
  'User is currently learning JavaScript',
  'User loves programming in TypeScript',
  'User has experience with TypeScript programming',
];
batchTest(queries).catch(console.error);
