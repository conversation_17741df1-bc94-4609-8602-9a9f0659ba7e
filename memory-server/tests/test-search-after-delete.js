const http = require('http');

function testSearch() {
  console.log('Testing search after delete...');

  const postData = JSON.stringify({
    query: 'cherry blossom',
    config: {
      filters: {
        userId: '48a02574f02e8dad07b7c3478ab81a042a59054a46a50fa15bed63e8270cec52',
      },
    },
  });

  const options = {
    hostname: 'localhost',
    port: 3000,
    path: '/memories/search',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData),
    },
    timeout: 5000, // 5 second timeout
  };

  const req = http.request(options, res => {
    console.log('Status:', res.statusCode);
    console.log('Headers:', res.headers);

    let data = '';
    res.on('data', chunk => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('Response:', data);
    });
  });

  req.on('error', error => {
    console.error('Request failed:', error.message);
  });

  req.on('timeout', () => {
    console.error('Request timed out');
    req.destroy();
  });

  req.write(postData);
  req.end();
}

testSearch();
