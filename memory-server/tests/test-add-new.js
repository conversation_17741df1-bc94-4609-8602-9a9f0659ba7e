const axios = require('axios');
const assert = require('assert');

const BASE = 'http://localhost:3000/memory';

async function run() {
  console.log('Testing add new memory (new API)...');

  // 1. add multiple similar but not exactly same memories, test aggregation logic
  let res = await axios.post(`${BASE}/add`, {
    messages: [
      {
        id: 1,
        conversation_id: 1753172351646,
        role: 'user',
        content: 'I will attend a conference  in September',
        status: 'pending',
      },
    ],
    config: {
      filters: {
        userId: 'test-user-1',
        conversationId: 1753172351646,
      },
      metadata: {},
    },
  });
  assert.strictEqual(res.status, 201);
  assert.deepStrictEqual(res.data.results, []);
  assert.deepStrictEqual(res.data.relations, []);
  console.log('✅ 1. add first similar memory passed');

  // 2. search all memories, verify if they are aggregated, should only have one aggregated memory left
  res = await axios.post(`${BASE}/search`, {
    text: 'conference Berlin September attend travel',
    config: {
      filters: {
        userId: 'test-user-1',
        conversationId: 1753172351646,
      },
      limit: 10,
    },
  });
  assert.strictEqual(res.status, 200);
  console.log(
    '2. current all memories:',
    res.data.results.map(r => r.memory)
  );
  assert.strictEqual(res.data.results.length, 1, 'should only have one aggregated memory');
  const memoryContent = res.data.results[0].memory;
  assert(
    memoryContent.includes('conference') && memoryContent.includes('Paris'),
    'aggregated content should contain all key information'
  );
  console.log('✅ 2. aggregated logic passed');

  // // 2. missing filters error
  // try {
  //   await axios.post(`${BASE}/add`, {
  //     messages: [
  //       { id: 2, conversation_id: 1002, role: 'user', content: 'fail', status: 'pending' },
  //     ],
  //     config: {},
  //   });
  //   assert.fail('should throw');
  // } catch (e) {
  //   assert(e.response.data.error.includes('config.userId is required'));
  //   console.log('✅ 3. missing filters error passed');
  // }

  // // 3. conversation_id mismatch error
  // try {
  //   await axios.post(`${BASE}/add`, {
  //     messages: [
  //       { id: 3, conversation_id: 1753172351646, role: 'user', content: 'msg1', status: 'pending' },
  //       { id: 4, conversation_id: 1753172351647, role: 'user', content: 'msg2', status: 'pending' }, // 改成不同的 id
  //     ],
  //     config: { filters: { userId: 'test-user-2' } },
  //   });
  //   assert.fail('should throw');
  // } catch (e) {
  //   assert(e.response.data.error.includes('All messages must have the same conversation_id'));
  //   console.log('✅ 4. conversation_id mismatch error passed');
  // }

  // 4. addSiteMessage passed
  // res = await axios.post(`${BASE}/add_site`, {
  //   messages: [
  //     {
  //       id: 10,
  //       conversation_id: 0,
  //       role: 'user',
  //       content: 'site memory test',
  //       status: 'pending',
  //     },
  //   ],
  //   config: {
  //     filters: {
  //       siteId: 'site-1',
  //       faqId: 'faq-1',
  //     },
  //     metadata: { foo: 'bar' },
  //   },
  // });
  // assert.strictEqual(res.status, 201);
  // assert.strictEqual(res.data.success, true);
  // console.log('✅ 5. addSiteMessage passed');

  // // 5. missing siteId error
  // try {
  //   await axios.post(`${BASE}/add_site`, {
  //     messages: [
  //       { id: 11, conversation_id: 0, role: 'user', content: 'fail', status: 'pending' },
  //     ],
  //     config: { filters: { faqId: 'faq-2' } },
  //   });
  //   assert.fail('should throw');
  // } catch (e) {
  //   assert(e.response.data.error.includes('config.filters.siteId is required'));
  //   console.log('✅ 6. addSiteMessage missing siteId error passed');
  // }

  // // 6. missing faqId error
  // try {
  //   await axios.post(`${BASE}/add_site`, {
  //     messages: [
  //       { id: 12, conversation_id: 0, role: 'user', content: 'fail', status: 'pending' },
  //     ],
  //     config: { filters: { siteId: 'site-2' } },
  //   });
  //   assert.fail('should throw');
  // } catch (e) {
  //   assert(e.response.data.error.includes('config.filters.faqId is required'));
  //   console.log('✅ 7. addSiteMessage missing faqId error passed');
  // }

  // console.log('All add memory tests passed!');
}

run().catch(e => {
  console.error(e);
  process.exit(1);
});
