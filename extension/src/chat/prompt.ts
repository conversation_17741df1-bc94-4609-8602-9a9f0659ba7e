import { MemoryItem } from '@the-agent/shared';
import { ContextChunk, SiteMessage } from '~/types';
import { GlobalContext, TaskWithState } from '~/types/task';

export function buildWebTabContextPrompt(
  activeTab: { id?: number; url?: string; title?: string } | undefined,
  availableTabs: { id?: number; url?: string; title?: string }[] = []
): ContextChunk | undefined {
  if (!activeTab || !activeTab.id || !activeTab.url) {
    return undefined;
  }

  let content = `Active tab: ${activeTab.url} (id: ${activeTab.id})`;
  if (activeTab.title) {
    content += ` - "${activeTab.title}"`;
  }

  if (availableTabs.length > 1) {
    content += '\n\nAvailable tabs:';
    availableTabs.forEach((tab, index) => {
      if (tab.id && tab.url && tab.id !== activeTab.id) {
        const title = tab.title ? ` - "${tab.title}"` : '';
        content += `\n${index + 1}. ${tab.url} (id: ${tab.id})${title}`;
      }
    });
  }

  return {
    title: '[Browser tab info starts here]',
    content,
    footer: '[Browser tab info ends here]',
  };
}

export function buildSiteMemoryContextPrompt(
  relatedSiteMessages: SiteMessage[] = []
): ContextChunk | undefined {
  if (relatedSiteMessages.length === 0) {
    return undefined;
  }
  let siteKnowledge = '';
  siteKnowledge = relatedSiteMessages
    .map(msg => `Q: ${msg.question}\nA: ${msg.answer}`)
    .join('\n\n');
  return {
    title: '[Site-specific Q&A Knowledge starts here]',
    content: siteKnowledge,
    footer: '[Site-specific Q&A Knowledge ends here]',
  };
}

export function buildRelatedMessagesContextPrompt({
  relatedMessages,
}: {
  relatedMessages: MemoryItem[];
}): ContextChunk[] {
  if (relatedMessages.length === 0) {
    return [];
  }
  // Just map all memory items into a single context chunk
  const content = relatedMessages.map(m => m.memory).join('\n\n');
  return [
    {
      title: '[Memory Facts]',
      content,
      footer: '[End Memory Facts]',
    },
  ];
}

export function buildMemoryFactsContextChunk(
  relatedMessages: MemoryItem[]
): ContextChunk | undefined {
  if (!relatedMessages.length) return undefined;
  return {
    title: ' [Memory extracted from conversation history]',
    content: relatedMessages.map(m => m.memory).join('\n\n'),
    footer: '[End Memory Facts]',
  };
}

export function buildGraphContextPrompt(graphRelations: any[] = []): ContextChunk | undefined {
  if (graphRelations.length === 0) {
    return undefined;
  }
  let contextPrompt = '';
  for (const rel of graphRelations) {
    const src = rel.source || rel.source_name || rel.from;
    const dst = rel.destination || rel.target || rel.to;
    const relType = rel.relationship || rel.relation || rel.type;
    contextPrompt += `- ${src} --[${relType}]--> ${dst}\n`;
  }
  return {
    title: '[Knowledge Graph Relations starts here]',
    content: contextPrompt,
    footer: '[Knowledge Graph Relations ends here]',
  };
}

export function buildTaskContextPrompt(task: TaskWithState, c: GlobalContext): ContextChunk[] {
  const chunks = [
    {
      title: '## Current Task:',
      content: JSON.stringify(task.task),
    },
    {
      title: '## Control Context:',
      content: `
- Execution Path: ${c.processing.join(' → ')}
- Depth: ${task.state.depth}
      `,
    },
  ];
  const runtimeInputs = task.state.runtimeInputProcessed ?? task.state.runtimeInputs;
  if (runtimeInputs && runtimeInputs.length > 0) {
    return [
      {
        title: '## Input Values: ',
        content: JSON.stringify(runtimeInputs),
      },
      ...chunks,
    ];
  } else {
    return chunks;
  }
}

export const createFAQPrompt = (
  actionSteps: Array<{
    question: string;
    answer: string;
    toolCall?: string;
  }>
): string => {
  const stepsText = actionSteps
    .map(
      (step, i) =>
        `${i + 1}. Question: ${step.question}\n   Action: ${step.answer}\n   Tool: ${step.toolCall || 'N/A'}`
    )
    .join('\n\n');

  return `Create Q&A pairs for these web automation steps:

${stepsText}

Requirements:
- Each question should be clear and actionable (e.g., "How to search for content")
- Each answer should combine helpful explanation with the tool call in this format: "Natural language explanation, ToolName({parameters})"
- Focus on user-friendly language first, then include the technical detail
- Maximum 5 pairs

Return ONLY this JSON format:
[
  {"question": "How to do something", "answer": "Brief helpful explanation, ToolName({parameters})"},
  {"question": "How to do another thing", "answer": "Another helpful explanation, AnotherTool({parameters})"}
]`;
};
