import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>xecutor, ToolOptions } from '@the-agent/shared';
import { finishTask, TASK_DONE_MESSAGE } from './task-toolkit';
import OpenA<PERSON> from 'openai';
import { parseToolParams, toOpenAITools } from '~/utils/toolkit';
import { GlobalContext, TaskStatus, TaskWithState } from '~/types/task';
import { ToolDescription } from '~/types';
import { AgentRuntimeConfig } from '@the-agent/shared/src/types/agent';

export abstract class TaskToolExecutor implements ToolExecutor {
  protected c: GlobalContext;

  constructor(c: GlobalContext) {
    this.c = c;
  }

  abstract getToolDescriptions(): ToolDescription[];

  abstract executeInternal(
    task: TaskWithState,
    name: string,
    params: any,
    options?: ToolOptions
  ): Promise<ToolCallResult>;

  abstract getPostToolcallMessageInternal(toolCall: ToolCall): string;

  getTools(): OpenAI.ChatCompletionTool[] {
    return toOpenAITools(this.getToolDescriptions());
  }

  async execute(toolCall: ToolCall, options?: AgentRuntimeConfig): Promise<ToolCallResult> {
    const taskId = options?.task_id;
    if (!taskId) {
      return {
        success: false,
        error: 'Task ID is required',
      };
    }
    const task = this.c.tasks[taskId];
    if (!task) {
      return {
        success: false,
        error: 'Invalid input: task not exists',
      };
    }
    const params = parseToolParams(toolCall);
    const toolName = toolCall.function.name;
    if (toolName === 'TaskToolkit_finishTask') {
      const p = params as { status: TaskStatus; output: string };
      return finishTask(this.c, task, p);
    }
    try {
      return await this.executeInternal(task, toolName, params, options?.toolOptions);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  getPostToolcallMessage(toolCall: ToolCall): string {
    if (!toolCall.result) {
      throw new Error('Tool call result is required');
    }
    if (toolCall.function.name === 'TaskToolkit_finishTask') {
      return TASK_DONE_MESSAGE;
    } else {
      return this.getPostToolcallMessageInternal(toolCall);
    }
  }

  isFinalToolCall(toolCall: ToolCall) {
    return toolCall.function.name === 'TaskToolkit_finishTask';
  }
}
