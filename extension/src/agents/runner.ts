import { GlobalContext, RuntimeInput, TaskResult, TaskState, TaskWithState } from '~/types/task';
import { Agent, Message, Task } from '@the-agent/shared';
import { finishTask } from '~/tools/task-toolkit';

export async function run(task: TaskWithState, c: GlobalContext): Promise<TaskResult> {
  const taskType = getTaskType(task.task);
  const agent = c.agents[taskType];
  if (!agent) {
    throw new Error(`${taskType} agent does not exist`);
  }
  return await runTask(task, agent, c);
}

export async function runTask(
  task: TaskWithState,
  agent: Agent,
  c: GlobalContext
): Promise<TaskResult> {
  c.processing.push(task.task.id);
  let message = buildTaskMessage(task.task, agent, c);
  while (true) {
    try {
      const config = {
        chatOptions: c.chatOptions,
        task_id: task.task.id,
      };
      const history = await agent.run(message, config);
      task.state.history.push(...history);
      const result = task.state.result;
      if (result) {
        return result;
      }
      message = buildContinueTaskMessage(task.task, agent.getConfig().id, c);
    } catch (error) {
      const result: TaskResult = {
        status: 'error',
        output: error instanceof Error ? error.message : 'Unknown error',
      };
      finishTask(c, task, result);
      return result;
    }
  }
}

export function buildTaskMessage(task: Task, agent: Agent, c: GlobalContext): Message {
  return {
    id: Date.now(),
    conversation_id: c.chatOptions.conversationId,
    role: 'user',
    content: `Now run the task provided.`,
    status: 'pending',
    actor: 'user',
    run_id: c.workflowId,
    agent_id: agent.getConfig().id,
    task_id: task.id,
  };
}

function getTaskType(task: Task) {
  if ('foreach' in task) {
    return 'foreach';
  }
  return 'dispatcher';
}

function buildContinueTaskMessage(task: Task, agent_id: string, c: GlobalContext): Message {
  return {
    id: Date.now(),
    conversation_id: c.chatOptions.conversationId,
    role: 'user',
    content: `
I checked the database and did not see any result stored. Have you finished the task? 

If yes, please complete the task with the TaskToolkit_finishTask tool.

If no, please continue the task.
`,
    status: 'pending',
    actor: 'system',
    run_id: c.workflowId,
    agent_id: agent_id,
    task_id: task.id,
  };
}

export function initTaskState(
  c: GlobalContext,
  task: Task,
  parent?: TaskWithState,
  input?: string
): TaskWithState {
  const taskWithState = c.tasks[task.id];
  if (taskWithState) {
    return taskWithState;
  }
  const depth = parent ? parent.state.depth + 1 : 0;
  const runtimeInputs = aggregateTaskOutputs(task.inputsFrom ?? [], c);
  const state: TaskState = {
    parentTaskId: parent?.task.id ?? null,
    depth,
    runtimeInputs,
    runtimeInputProcessed: input,
    history: [],
  };
  const newTaskWithState: TaskWithState = { task, state };
  c.tasks[task.id] = newTaskWithState;
  return newTaskWithState;
}

function aggregateTaskOutputs(taskIds: string[], c: GlobalContext): RuntimeInput[] {
  return taskIds
    .map(taskId => {
      const result = c.tasks[taskId].state.result;
      if (result) {
        return {
          id: taskId,
          ...result,
        };
      }
    })
    .filter(r => r !== undefined);
}
