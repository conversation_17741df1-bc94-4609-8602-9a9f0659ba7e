import OpenAI from 'openai';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Agent,
  AgentConfig,
  ForeachTask,
  ToolCall,
  ToolCallResult,
  ToolOptions,
} from '@the-agent/shared';
import { GlobalContext, RuntimeInput, TaskWithState } from '~/types/task';
import { taskTools } from '~/tools/task-toolkit';
import { initTaskState, run } from './runner';
import { TaskContextBuilder } from './context';
import { DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';

class ForeachToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(
    task: TaskWithState,
    toolName: string,
    params: any,
    options?: ToolOptions
  ): Promise<ToolCallResult> {
    switch (toolName) {
      case 'ForeachToolkit_iterate':
        const p1 = params as { inputs: string[] | undefined };
        return this.iterate(p1.inputs, task, options);
      case 'ForeachToolkit_repeat':
        const p2 = params as { count: number };
        return this.repeat(p2.count, task, options);
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'ForeachToolkit_iterate',
        description: 'Iterate over given input array and run the subtask for each input',
        parameters: {
          type: 'object',
          properties: {
            inputs: {
              type: 'array',
              description: 'The input for the subtask.',
              items: {
                type: 'string',
              },
            },
          },
          required: ['inputs'],
        },
        returns: {
          type: 'object',
          description: 'The task results collected from the iteration',
          properties: {
            results: {
              type: 'array',
              description: 'The task results collected from the iteration',
              items: {
                type: 'object',
                properties: {
                  goal: { type: 'string', description: 'The goal of the task' },
                  status: { type: 'string', description: 'The status of the task' },
                  output: { type: 'string', description: 'The output of the task' },
                },
              },
            },
          },
        },
      },
      {
        name: 'ForeachToolkit_repeat',
        description: 'Repeat the subtask for given number of times',
        parameters: {
          type: 'object',
          properties: {
            count: {
              type: 'number',
              description: 'The number of times to repeat the subtask.',
            },
          },
          required: ['count'],
        },
        returns: {
          type: 'object',
          description: 'The task results collected from the iteration',
          properties: {
            results: {
              type: 'array',
              description: 'The task results collected from the iteration',
              items: {
                type: 'object',
                properties: {
                  goal: { type: 'string', description: 'The goal of the task' },
                  status: { type: 'string', description: 'The status of the task' },
                  output: { type: 'string', description: 'The output of the task' },
                },
              },
            },
          },
        },
      },
      ...taskTools,
    ];
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'ForeachToolkit_iterate') {
      if (toolCall.result?.success) {
        return FOREACH_AGENT_ITERATE_SUCCESS_MESSAGE;
      } else {
        return FOREACH_AGENT_ITERATE_ERROR_MESSAGE;
      }
    } else if (toolCall.function.name === 'ForeachToolkit_repeat') {
      if (toolCall.result?.success) {
        return FOREACH_AGENT_REPEAT_SUCCESS_MESSAGE;
      } else {
        return FOREACH_AGENT_REPEAT_ERROR_MESSAGE;
      }
    } else {
      throw new Error('Invalid tool call');
    }
  }

  private async iterate(
    inputs: string[] | undefined,
    task: TaskWithState,
    options?: ToolOptions
  ): Promise<ToolCallResult> {
    if (!inputs) {
      return {
        success: false,
        error: 'Invalid input: inputs is required',
      };
    }

    const st = (task.task as ForeachTask).foreach.task;
    const results: RuntimeInput[] = [];
    const subtasks = await Promise.all(
      inputs.map((input, i) => initTaskState(this.c, { ...st, id: `${st.id}_${i}` }, task, input))
    );
    for (const subtask of subtasks) {
      const result = await run(subtask, this.c);
      results.push({ id: subtask.task.id, ...result });
      await options?.onToolMessageUpdate?.(results);
    }
    return {
      success: true,
      data: results,
    };
  }

  private async repeat(
    count: number,
    task: TaskWithState,
    options?: ToolOptions
  ): Promise<ToolCallResult> {
    const subtask = (task.task as ForeachTask).foreach.task;
    const results: RuntimeInput[] = [];
    for (let i = 0; i < count; i++) {
      const st = { ...subtask, id: `${subtask.id}_run${i}` };
      const state = initTaskState(this.c, st, task);
      const result = await run(state, this.c);
      results.push({ id: subtask.id, ...result });
      await options?.onToolMessageUpdate?.(results);
    }
    return {
      success: true,
      data: results,
    };
  }
}

export function createForeachAgent(model: string, openai: OpenAI, c: GlobalContext): Agent {
  const config: AgentConfig = {
    id: 'foreach',
    llmClient: openai,
    model: model,
    systemPrompt: FOREACH_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(c),
    toolExecutor: new ForeachToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  };
  return new ChatAgent(config);
}

export const FOREACH_AGENT_ITERATE_SUCCESS_MESSAGE = `
You have successfully executed the task via ForeachToolkit_iterate to iterate over the input array and collected the results.

Now summarize the result and store them with the TaskToolkit_finishTask tool.`;

export const FOREACH_AGENT_ITERATE_ERROR_MESSAGE = `
You have failed to execute the task via ForeachToolkit_iterate.

You can either retry the task by running the ForeachToolkit_repeat tool again, or complete the task.

- You should retry the task **only if** the task is stateless or recoverable.

- If you think the error is not recoverable or doesn't make sense to retry, you can complete the task with error status and error message via the TaskToolkit_finishTask tool.
`;

export const FOREACH_AGENT_REPEAT_SUCCESS_MESSAGE = `
You have successfully executed the task via ForeachToolkit_repeat.

Now summarize the result and store them with the TaskToolkit_finishTask tool.`;

export const FOREACH_AGENT_REPEAT_ERROR_MESSAGE = `
You have failed to execute the task via ForeachToolkit_repeat.

You can either retry the task by running the ForeachToolkit_repeat tool again, or complete the task.

- You should retry the task **only if** the task is stateless or recoverable.

- If you think the error is not recoverable or doesn't make sense to retry, you can complete the task with error status and error message via the TaskToolkit_finishTask tool.
`;

export const FOREACH_SYSTEM_PROMPT = `
You are a task executor handling a special task with a \`foreach\` control structure.

The task is defined in the following format:

\`\`\`json
Task {
  id: string              // Unique identifier for the task (e.g. "open-tab", "click-button")
  goal: string            // Description of what this task is meant to accomplish
  inputsFrom?: string[]   // Optional. IDs of tasks this task depends on
  output?: string         // Description of the expected final output format
  foreach: {
    count?: number        // Optional. Number of iterations (if static)
    task: Task            // The inner task to execute in each iteration
  }
}
\`\`\`

---
## Guidelines

You may use the following tools to complete the task depending on the situation:

### 🔁 \`ForeachToolkit_repeat\` — Use when \`count\` is provided
Repeat the inner task a fixed number of times.

\`\`\`json
{
  "foreach": {
    "count": 3,
    "task": { ... }
  }
}
\`\`\`

### 🔄 \`ForeachToolkit_iterate\` — Use when iterating over input data
Iterate over a list of items from tasks listed in \`inputsFrom\`.

\`\`\`json
{
  "inputsFrom": ["task1"],
  "foreach": {
    "task": { ... }
  }
}
\`\`\`


### TaskToolkit_finishTask - use to complete the task with proper result or error message

## 📋 Execution Guidelines

- You do **not** execute the inner task directly — your job is to instruct the system to repeat or iterate it.
- You do **not** need to know how the inner task is executed.
- You should always return an aggregated result matching the parent task's \`output\` field.
- Use the appropriate toolkit function based on whether the loop is driven by a fixed count or dynamic inputs.

## 📤 Final Output

After all iterations complete:
- Aggregate the results from each run
- Format the output as described in the parent task’s \`output\`
`;
