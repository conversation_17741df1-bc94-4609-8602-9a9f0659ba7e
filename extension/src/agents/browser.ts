import {
  Agent,
  AgentConfig,
  APIClient,
  ChatAgent,
  ContextBuilder,
  MemoryOptions,
  Message,
  SearchMemoryResponse,
} from '@the-agent/shared';
import {
  buildMemoryFactsContextChunk,
  buildSiteMemoryContextPrompt,
  buildTaskContextPrompt,
  buildWebTabContextPrompt,
} from '~/chat/prompt';
import { ContextChunk, SiteMessage } from '~/types';
import OpenAI from 'openai';
import { db } from '~/storages/indexdb';
import { GlobalContext } from '~/types/task';
import { buildContext, filterRecentMessages } from '~/utils/context';
import { DEFAULT_MAX_BROWSER_TOOL_CALLS } from '~/configs/common';
import { BrowserTaskToolExecutor, BrowserToolExecutor } from '~/tools/browser-executor';

const BROWSER_MEMORY_OPTIONS: MemoryOptions = {
  recent: 5,
  related: 5,
  site: 5,
  graph: 0,
  tab: true,
};

export class BrowserAgentContextBuilder implements ContextBuilder {
  private apiClient: APIClient;
  private c: GlobalContext | undefined;

  constructor(apiClient: APIClient, c?: GlobalContext) {
    this.apiClient = apiClient;
    this.c = c;
  }

  async build(message: Message, options?: Partial<MemoryOptions>): Promise<Message[]> {
    const memoryOptions = { ...BROWSER_MEMORY_OPTIONS, ...(options ?? {}) };
    const [availableTabs, activeTab] = await Promise.all([
      chrome.tabs.query({ currentWindow: true }),
      chrome.tabs.query({ active: true, currentWindow: true }).then(tabs => tabs[0]),
    ]);
    const [searchResponse, relatedSiteMessages, recentMessages] = await Promise.all([
      this.searchMemory(message, memoryOptions.related),
      this.searchSiteMessages({
        url: activeTab?.url,
        message: message.content,
        limit: memoryOptions.site,
      }),
      this.getRecentMessages(message, memoryOptions.recent),
    ]);

    const chunks: ContextChunk[] = [
      buildMemoryFactsContextChunk(searchResponse.results),
      buildSiteMemoryContextPrompt(relatedSiteMessages),
      buildWebTabContextPrompt(activeTab, availableTabs),
    ].filter(chunk => chunk !== undefined);
    if (this.c && message.task_id) {
      const task = this.c.tasks[message.task_id];
      chunks.push(...buildTaskContextPrompt(task!, this.c));
    }
    message.content = buildContext(message, chunks);
    return [...filterRecentMessages(recentMessages), message];
  }

  private async searchSiteMessages({
    url,
    message,
    limit,
  }: {
    url?: string;
    message?: string | null;
    limit: number;
  }): Promise<SiteMessage[]> {
    if (!url || !message || url.startsWith('chrome://') || limit <= 0) {
      return [];
    }
    let relatedSiteMessages: SiteMessage[] = [];
    try {
      const siteSearchResponse = await this.apiClient.searchSiteMessage({
        site: url,
        user_message: message,
        limit: limit,
      });
      relatedSiteMessages = siteSearchResponse.related_site_messages || [];
    } catch (error) {
      console.warn('Failed to search site messages:', error);
      // Continue without site-specific context
    }
    return relatedSiteMessages;
  }

  private async searchMemory(message: Message, limit: number = 3): Promise<SearchMemoryResponse> {
    if (!message.content || limit <= 0) {
      return {
        results: [],
        relations: [],
      };
    }
    return await this.apiClient.searchMemory({
      text: message.content || '',
      limit,
      conversationId: message.conversation_id,
    });
  }

  private async getRecentMessages(message: Message, limit: number): Promise<Message[]> {
    if (message.task_id && this.c) {
      const task = this.c.tasks[message.task_id];
      if (!task) {
        throw new Error(`Task ${message.task_id} not found`);
      }
      return task.state.history.slice(-limit);
    } else {
      return await db.getRecentMessages(message.conversation_id, limit);
    }
  }
}

export function createBrowserAgent(model: string, openai: OpenAI, apiClient: APIClient): Agent {
  const contextBuilder = new BrowserAgentContextBuilder(apiClient);
  const config: AgentConfig = {
    id: 'browser',
    llmClient: openai,
    model: model,
    systemPrompt: BROWSER_AGENT_SYSTEM_PROMPT,
    toolExecutor: new BrowserToolExecutor(),
    maxToolCalls: DEFAULT_MAX_BROWSER_TOOL_CALLS,
    contextBuilder,
  };
  return new ChatAgent(config);
}

export function createTaskBrowserAgent(
  model: string,
  openai: OpenAI,
  apiClient: APIClient,
  c: GlobalContext
): Agent {
  const contextBuilder = new BrowserAgentContextBuilder(apiClient, c);
  const config: AgentConfig = {
    id: 'browser',
    llmClient: openai,
    model: model,
    systemPrompt: TASK_BROWSER_AGENT_SYSTEM_PROMPT,
    toolExecutor: new BrowserTaskToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_BROWSER_TOOL_CALLS,
    contextBuilder,
  };
  return new ChatAgent(config);
}

const BROWSER_AGENT_SYSTEM_PROMPT = `You are "Mysta", the AI agent with a secret: you can control browsers.
You will be given a user's request and information about the current web page. Your goal is to break down the request into a series of tool calls to accomplish the task.

You will be provided with the following information:
- **Task history memory**: A summary of what has been done so far.
- **Site-specific Q&A Knowledge**: Relevant knowledge base for the current action, formatted as Q: question / A: answer pairs.
- **Current URL**: The URL of the active browser tab.

Based on this information, decide on the next best action or sequence of actions to take using the available tools.

**OPTIMIZATION RULE - Using Site Knowledge:**
Before calling \`buildElementMap\`, check if the **Site-specific Q&A Knowledge** section contains relevant information for your current task:
- If the Q&A contains actions similar to what you need to perform (e.g., "Search for content", "Click search button", "Input text"), use the exact tool calls from the answers.
- The answers contain tool function calls with hash keys that you can reuse directly.
- If the hash key not found in the element map, try to find the element by using \`sendKeys({"keys":"Space"})\` and then call tool function again.
- **XPATH OPTIMIZATION**: If Q&A contains a fullXPath reference for the specific element you need, call \`buildElementMap({"fullXPath": "<xpath>"})\` to build a focused element map for just that element and its descendants, which is more efficient.
- Only call \`buildElementMap()\` without parameters if no relevant Q&A knowledge is available for your current task.
- This saves processing time and tokens while maintaining effectiveness.

**IMPORTANT RULE FOR SEARCHING:**
When performing a search task, if a clear 'search' button is not available, your default procedure should be two steps:
1. Use the \`input_text\` tool to enter the search query into the appropriate input field.
2. Use the \`send_keys\` tool with "Enter" to submit the form.

**IMPORTANT RULE FOR STOPPING:**
- Please try your best to finish the user request before stopping.
- Only stop to ask for user inputs when it's really necessary.
`;

const TASK_BROWSER_AGENT_SYSTEM_PROMPT = `You are **Mysta**, an AI agent with a secret: you can control browsers.

Your job is to accomplish the user’s request by reasoning about the web page and invoking the appropriate tools step by step.

You will be provided with:
- **Task Related Context** – a summary of the task and the related context
- **Site-Specific Q&A Knowledge** – known interaction patterns for this website, in Q: / A: format (answers may include tool call templates)
- **Current URL** – the page you're currently working on

---

## 🧠 Your Mission
Break the task into concrete browser actions and call tools to complete it. Always choose the **next best step**, and use available knowledge and memory to avoid redundant actions.

---

## ⚙️ TOOL USAGE STRATEGY

### ✅ Site Knowledge Optimization
- Before calling \`buildElementMap\`, check the **Site Q&A Knowledge**:
  - If the Q&A includes tool call examples matching your goal (e.g. “click search button”, “input text”), **reuse** the suggested function calls directly.
  - Use the provided tool call **hash keys** from the Q&A answers.
  - Only call \`buildElementMap\` if no matching behavior is found.
- ⚠️ This saves time, tokens, and avoids redundant element scans.

### 🔍 Searching Procedure (if no clear search button exists)
1. Use \`input_text\` to type the query into the correct field.
2. Use \`send_keys\` with the "Enter" key to submit.

This applies to most search bars when a dedicated button is not found.

---

## 🚧 Error Handling and Retrying
- If your action fails (e.g., wrong element, blocked input), you may retry a different approach or element.
- Use updated memory to avoid retrying the same failing element again.

---

## 📥 Task Completion
Once the task is complete or cannot proceed further:

- You **must** call \`TaskToolkit_finishTask\` to save either:
  - The final output (e.g. a URL, confirmation message, list of data)
  - Or an error message and mark the task as failed
`;
