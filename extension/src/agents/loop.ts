import OpenA<PERSON> from 'openai';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Agent,
  AgentConfig,
  ToolCallResult,
  LoopTask,
  <PERSON>lCall,
} from '@the-agent/shared';
import { GlobalContext, TaskWithState } from '~/types/task';
import { taskTools } from '~/tools/task-toolkit';
import { initTaskState, run } from './runner';
import { TaskContextBuilder } from './context';
import { DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';

class LoopToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(task: TaskWithState, toolName: string): Promise<ToolCallResult> {
    switch (toolName) {
      case 'LoopToolkit_run':
        return this.runSubtask(task);
      case 'LoopToolkit_check':
        return this.check(task);
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'LoopToolkit_run',
        description: 'Run the subtask and get the result',
        parameters: {
          type: 'object',
          properties: {},
          required: [],
        },
        returns: {
          type: 'object',
          description: 'The result of the subtask in loop field',
          properties: {
            status: { type: 'string', description: 'The status of the task' },
            output: { type: 'string', description: 'The output of the task' },
          },
        },
      },
      {
        name: 'LoopToolkit_check',
        description: 'Check if the task is completed',
        parameters: {
          type: 'object',
          properties: {},
          required: [],
        },
        returns: {
          type: 'object',
          description: 'The result of the checker task in loop field',
          properties: {
            status: { type: 'string', description: 'The status of the task' },
            output: { type: 'string', description: 'The output of the task' },
          },
        },
      },
      ...taskTools,
    ];
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'LoopToolkit_run') {
      if (toolCall.result?.success) {
        return LOOP_RUN_SUCCESS_MESSAGE;
      } else {
        return LOOP_RUN_ERROR_MESSAGE;
      }
    } else if (toolCall.function.name === 'LoopToolkit_check') {
      if (toolCall.result?.success) {
        return LOOP_CHECK_SUCCESS_MESSAGE;
      } else {
        return LOOP_CHECK_ERROR_MESSAGE;
      }
    } else {
      throw new Error('Invalid tool call');
    }
  }

  private async runSubtask(task: TaskWithState): Promise<ToolCallResult> {
    const subtask = (task.task as LoopTask).loop.task;
    const st = { ...subtask, id: `${subtask.id}_run_${Date.now()}` };
    const state = initTaskState(this.c, st, task);
    const result = await run(state, this.c);
    return {
      success: true,
      data: result,
    };
  }

  private async check(task: TaskWithState): Promise<ToolCallResult> {
    const checker = (task.task as LoopTask).loop.until;
    const st = { ...checker, id: `${checker.id}_run_${Date.now()}` };
    const state = initTaskState(this.c, st, task);
    const result = await run(state, this.c);
    return {
      success: true,
      data: result,
    };
  }
}

export function createLoopAgent(model: string, openai: OpenAI, c: GlobalContext): Agent {
  const config: AgentConfig = {
    id: 'loop',
    llmClient: openai,
    model: model,
    systemPrompt: LOOP_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(c),
    toolExecutor: new LoopToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  };
  return new ChatAgent(config);
}

const LOOP_RUN_SUCCESS_MESSAGE = `You have successfully executed the task via LoopToolkit_run to run the loop.task and collected the results.

Now you can run LoopToolkit_check tool to check if the loop should continue.
`;

const LOOP_RUN_ERROR_MESSAGE = `
You have failed to execute the task via LoopToolkit_run to run the loop.task.

You can either retry the task by running the LoopToolkit_run tool again, or complete the task via the TaskToolkit_finishTask tool.
`;

const LOOP_CHECK_SUCCESS_MESSAGE = `
You have successfully executed the task via LoopToolkit_check to check if the loop should continue.

Next, check the result from LoopToolkit_check tool call and:

- If the loop should continue, you should run LoopToolkit_run tool to run the loop.task again.

- If the loop should not continue, you should complete the task via the TaskToolkit_finishTask tool.
`;

const LOOP_CHECK_ERROR_MESSAGE = `
You have failed to execute the task via LoopToolkit_check to check if the loop should continue.

You can either retry the task by running the LoopToolkit_check tool again, or complete the task via the TaskToolkit_finishTask tool.
`;

const LOOP_SYSTEM_PROMPT = `
You are an task executor to process task defined as following json:

\`\`\`json
Task {
    id: string              // Unique identifier (e.g. "open-tab", "click-button")
    goal: string            // Description of what this task does
    inputsFrom?: string[]   // Optional. IDs of tasks this task depends on
    output?: string         // content and format of the expected result
    loop: {
      task: Task             // Task to execute for each iteration
      until: Task            // Task to check if the loop should continue
    }
}
\`\`\`


## Guidelines

You can use the following tools to complete the task:

- LoopToolkit_run: Run the loop.task and get the result

- LoopToolkit_check: Run the loop.until task and get the result

- TaskToolkit_finishTask: complete the task with proper output

## Important Note

- You should only use the tools provided to complete the task.
- You should use result of LoopToolkit_check to decide if the loop should continue.
- Keep calling LoopToolkit_run until the loop should continue is false.
- The task execution is not visible to you, you don't need to know the details of the function execution, just use the tools to complete the task.

## Output format

You should aggregate the result of the task collected from LoopToolkit_run or LoopToolkit_check
and return the result per output field required.
`;
