import { Message as SMessage } from '@the-agent/shared';
import OpenAI from 'openai';
import { db } from '~/storages/indexdb';
import { env } from '~/configs/env';
import { DEFAULT_MODEL } from '~/configs/common';
import { createFAQPrompt } from '~/chat/prompt';

interface ActionStep {
  question: string;
  answer: string;
  toolCall?: string;
}

/**
 * Generate FAQs from conversation messages
 */
export const generateFAQsFromConversation = async (
  messages: SMessage[]
): Promise<{ question: string; answer: string }[]> => {
  try {
    const actionSteps = extractActionSteps(messages);

    if (actionSteps.length === 0) {
      console.log('No action steps found in conversation');
      return [];
    }

    // Try AI generation first, fallback to simple extraction
    try {
      const faqs = await generateFAQsWithAI(actionSteps);
      if (faqs.length > 0) return faqs;
    } catch (error) {
      console.warn('AI generation failed, using simple extraction:', error);
    }

    return generateSimpleFAQs(actionSteps);
  } catch (error) {
    console.error('Error generating FAQs:', error);
    return [];
  }
};

/**
 * Extract meaningful action steps from messages
 */
const extractActionSteps = (messages: SMessage[]): ActionStep[] => {
  const steps: ActionStep[] = [];

  for (let i = 0; i < messages.length; i++) {
    const message = messages[i];

    if (message.role === 'assistant' && message.tool_calls?.length) {
      const assistantText = message.content || '';

      // Skip trivial actions
      const meaningfulTools = message.tool_calls.filter(
        call =>
          call.function?.name &&
          !call.function.name.includes('getPageText') &&
          !call.function.name.includes('buildElementMap')
      );

      meaningfulTools.forEach(toolCall => {
        if (!toolCall.function) return;

        const toolCallText = `${toolCall.function.name}(${toolCall.function.arguments})`;
        const question = generateQuestionFromTool(toolCall.function.name, assistantText);

        // Combine natural language description with tool call
        const combinedAnswer = assistantText
          ? `${assistantText}, ${toolCallText}`
          : `Execute ${toolCall.function.name}, ${toolCallText}`;

        steps.push({
          question,
          answer: combinedAnswer,
          toolCall: toolCallText,
        });
      });
    }
  }

  return steps.slice(0, 5); // Limit to 5 most recent
};

/**
 * Generate question from tool name and context
 */
const generateQuestionFromTool = (toolName: string, context: string): string => {
  const toolMappings: Record<string, string> = {
    TabToolkit_openTab: 'How to open a website',
    WebToolkit_inputElementByIndex: 'How to input text',
    WebToolkit_clickElementByIndex: 'How to click an element',
    WebToolkit_sendKeys: 'How to use keyboard shortcuts',
    WebToolkit_scrollPage: 'How to scroll the page',
    WebToolkit_takeScreenshot: 'How to take a screenshot',
  };

  // Use context keywords to make question more specific
  const lowerContext = context.toLowerCase();
  if (lowerContext.includes('search')) return 'How to search for content';
  if (lowerContext.includes('login')) return 'How to log in';
  if (lowerContext.includes('submit')) return 'How to submit a form';

  return toolMappings[toolName] || `How to use ${toolName.replace(/([A-Z])/g, ' $1').trim()}`;
};

/**
 * Use AI to generate enhanced FAQs
 */
const generateFAQsWithAI = async (
  actionSteps: ActionStep[]
): Promise<{ question: string; answer: string }[]> => {
  const prompt = createFAQPrompt(actionSteps);

  const model = await db.getSelectModel();
  const client = new OpenAI({
    apiKey: model.apiKey,
    baseURL: env.BACKEND_URL + '/v1',
    dangerouslyAllowBrowser: true,
  });

  const response = await client.chat.completions.create({
    model: model.id === 'system' ? DEFAULT_MODEL : model.name || '',
    messages: [
      {
        role: 'system',
        content: 'Generate clear Q&A pairs for web automation tasks. Return only valid JSON array.',
      },
      { role: 'user', content: prompt },
    ],
    temperature: 0.3,
    max_tokens: 1000,
  });

  const content = response.choices[0]?.message?.content;
  if (!content) throw new Error('No AI response');

  const jsonContent = extractJsonFromResponse(content);
  const faqs = JSON.parse(jsonContent);

  if (!Array.isArray(faqs)) throw new Error('Invalid AI response format');

  return faqs
    .filter(
      faq =>
        faq.question &&
        faq.answer &&
        typeof faq.question === 'string' &&
        typeof faq.answer === 'string'
    )
    .slice(0, 5);
};

/**
 * Extract JSON from AI response, handling markdown
 */
const extractJsonFromResponse = (response: string): string => {
  const trimmed = response.trim();

  // Extract from markdown code blocks
  const codeBlockMatch = trimmed.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
  if (codeBlockMatch) return codeBlockMatch[1].trim();

  // Extract JSON array
  const jsonMatch = trimmed.match(/(\[[\s\S]*\])/);
  if (jsonMatch) return jsonMatch[1].trim();

  return trimmed;
};

/**
 * Generate simple FAQs as fallback
 */
const generateSimpleFAQs = (actionSteps: ActionStep[]): { question: string; answer: string }[] => {
  return actionSteps
    .filter(step => step.answer.length > 10)
    .map(step => ({
      question: step.question,
      answer: step.answer, // Already in the correct format from extractActionSteps
    }))
    .filter((faq, index, self) => index === self.findIndex(f => f.answer === faq.answer));
};
