import React, { useRef, useState } from 'react';
import { useLanguage } from '../../utils/i18n';
import { ChatStatus } from '@the-agent/shared';
import WorkflowIcon from '~/assets/icons/workflow.svg';

interface InputAreaProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  status: ChatStatus;
  abort: () => void;
  onAttachFile?: () => void;
  onRecordAudio?: () => void;
  workflowMode: boolean;
  onWorkflowModeChange: (enabled: boolean) => void;
}

export default function InputArea({
  prompt,
  setPrompt,
  onSubmit,
  status,
  abort,
  workflowMode,
  onWorkflowModeChange,
}: InputAreaProps) {
  const { getMessage } = useLanguage();
  const [isHovered, setIsHovered] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // 阻止默认的换行行为
      if (prompt.trim() && status === 'idle') {
        onSubmit(e);
      }
    }
  };

  // 使用useRef和useEffect自动调整textArea高度
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  return (
    <div style={{ position: 'relative' }}>
      <form
        style={{
          display: 'flex',
          flexDirection: 'column',
          padding: '0px 16px',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            borderRadius: '18px',
            border: `1px solid ${isHovered || isFocused ? '#333333' : '#e5e7eb'}`,
            background: '#ffffff',
            overflow: 'hidden',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
            transition: 'all 0.2s ease',
          }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div style={{ display: 'flex', flexDirection: 'row' }}>
            <textarea
              ref={textareaRef}
              value={prompt}
              onChange={e => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              disabled={status !== 'idle'}
              placeholder={getMessage('typeMessage')}
              rows={1}
              style={{
                flex: 1,
                padding: '10px 16px',
                height: '42px',
                outline: 'none',
                resize: 'none',
                border: 'none',
                backgroundColor: 'transparent',
                color: '#333333',
                fontSize: '13px',
                lineHeight: '1.5',
                fontWeight: 'normal',
                boxSizing: 'border-box',
              }}
            />
          </div>

          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '0 8px 6px 8px',
              gap: '8px',
            }}
          >
            <div style={{ marginLeft: '2px' }}>
              <button
                type="button"
                onClick={() => onWorkflowModeChange(!workflowMode)}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  padding: '6px 12px',
                  borderRadius: '16px',
                  border: 'none',
                  backgroundColor: workflowMode ? '#E8EFFF' : '#f3f4f6',
                  color: workflowMode ? '#2762ED' : '#6b7280',
                  fontSize: '12px',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                }}
              >
                <img
                  src={WorkflowIcon}
                  alt="Workflow"
                  style={{ width: 18, height: 18, opacity: 0.8 }}
                />
                Workflow
              </button>
            </div>

            <div style={{ marginRight: '2px' }}>
              {status !== 'idle' ? (
                <button
                  type="button"
                  onClick={abort}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    backgroundColor: '#333333',
                    border: 'none',
                    cursor: 'pointer',
                    transition: 'transform 0.2s, background-color 0.2s',
                  }}
                  onMouseOver={e => {
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                  }}
                  onMouseOut={e => {
                    e.currentTarget.style.backgroundColor = '#333333';
                  }}
                  onMouseDown={e => {
                    e.currentTarget.style.transform = 'scale(0.95)';
                  }}
                  onMouseUp={e => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                >
                  <svg
                    style={{ width: '14px', height: '14px', color: '#ffffff' }}
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <rect x="5" y="5" width="14" height="14" rx="1" />
                  </svg>
                </button>
              ) : (
                <button
                  type="submit"
                  onClick={onSubmit}
                  disabled={!prompt.trim()}
                  aria-label={getMessage('send')}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    backgroundColor: !prompt.trim() ? '#e5e7eb' : '#333333',
                    border: 'none',
                    cursor: !prompt.trim() ? 'not-allowed' : 'pointer',
                    opacity: !prompt.trim() ? 0.5 : 1,
                    transition: 'transform 0.2s, background-color 0.2s',
                    padding: '5px',
                  }}
                  onMouseOver={e => {
                    if (prompt.trim()) {
                      e.currentTarget.style.backgroundColor = '#000000';
                    }
                  }}
                  onMouseOut={e => {
                    if (prompt.trim()) {
                      e.currentTarget.style.backgroundColor = '#333333';
                    }
                  }}
                  onMouseDown={e => {
                    if (prompt.trim()) {
                      e.currentTarget.style.transform = 'scale(0.95)';
                    }
                  }}
                  onMouseUp={e => {
                    if (prompt.trim()) {
                      e.currentTarget.style.transform = 'scale(1)';
                    }
                  }}
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-white dark:text-black"
                  >
                    <path
                      d="M7 11L12 6L17 11M12 18V7"
                      stroke="white"
                      strokeWidth="1.8"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    ></path>
                  </svg>
                </button>
              )}
            </div>
          </div>
        </div>

        <p
          style={{
            fontSize: '12px',
            marginTop: '8px',
            textAlign: 'center',
            color: '#6b7280',
          }}
        >
          {getMessage('privacyDisclaimer')}
        </p>
      </form>
    </div>
  );
}
